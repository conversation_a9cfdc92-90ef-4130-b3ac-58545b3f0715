#include "../include/Player.hpp"
#include <SFML/Graphics.hpp>
#include <cmath>

Player::Player(){
    //创建一个半径为20的圆形表示玩家
    playerShape = new sf::CircleShape(20.0f);
    playerShape->setFillColor(sf::Color::Green);
    playerShape->set<PERSON><PERSON>in(20.0f, 20.0f); // 设置原点为圆心
        //初始化玩家位置在窗口中心
    setPosition(400.0f, 300.0f);
    //初始化玩家速度恒定为3
    setSpeed(3.0f);
    //初始化玩家方向为向右
    setDirection(1.0f, 0.0f);
}

sf::CircleShape* Player::getShape() const {
    return playerShape;
}

sf::Vector2f Player::getPosition() const {
    return position;
}

void Player::setPosition(float x, float y) {
    position = sf::Vector2f(x, y);
    playerShape->setPosition(position);
}

void Player::setDirection(float x, float y) {
    // 归一化方向向量
    float length = std::sqrt(x * x + y * y);
    direction = sf::Vector2f(x / length, y / length);
}

void Player::setSpeed(float s) {
    speed = s;
}

void Player::move(float deltaTime) {
    position += direction * speed * deltaTime;
    playerShape->setPosition(position);
}

Player::~Player() {
    delete playerShape;
}