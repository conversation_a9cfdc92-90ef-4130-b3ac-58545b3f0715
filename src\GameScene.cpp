#include "../include/GameScene.hpp"
#include <SFML/Graphics.hpp>

GameScene::GameScene(int width, int height, const char* title){
    window = new sf::RenderWindow(sf::VideoMode(width, height), title);
    window->setFramerateLimit(60);
    player1 = new Player();
    player2 = new Player();
    while(window->isOpen()){
        sf::Event event;
        while(window->pollEvent(event)){
            if(event.type == sf::Event::Closed){
                window->close();
            }
        }
        processInput();
        window->clear(sf::Color::White);
        // Drawing code goes here
        window->draw(*player1->getShape());
        window->draw(*player2->getShape());
        window->display();
    }
}
GameScene::~GameScene(){
    delete window;
}

void GameScene::processInput(){
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::W)){
        // Move up
        player1->setDirection(0.0f, -1.0f);
    }
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::A)){
        // Move left
        player1->setDirection(-1.0f, 0.0f);
    }
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::S)){
        // Move down
        player1->setDirection(0.0f, 1.0f);
    }
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::D)){
        // Move right
        player1->setDirection(1.0f, 0.0f);
    }
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::Up)){
        // Move up
        player2->setDirection(0.0f, -1.0f);
    }
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::Left)){
        // Move left
        player2->setDirection(-1.0f, 0.0f);
    }
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::Down)){
        // Move down
        player2->setDirection(0.0f, 1.0f);
    }
    if(sf::Keyboard::isKeyPressed(sf::Keyboard::Right)){
        // Move right
        player2->setDirection(1.0f, 0.0f);
    }
    // Update player positions
    player1->move(1.0f); // Assuming a deltaTime of 1
    player2->move(1.0f);
}